/**
 * PhishGuard Content Script
 * Performs DOM analysis, keyword detection, and displays in-page alerts
 */

// Import error handling (if available)
try {
  if (typeof importScripts !== 'undefined') {
    importScripts('error-handler.js');
  }
} catch (error) {
  // Fallback error handling for content scripts
  console.warn('Could not load error handler in content script');
}

// Content script state
let pageAnalysis = {
  phishingKeywords: [],
  insecureForms: [],
  suspiciousElements: [],
  threatLevel: 'safe'
};

let notificationTimeout = null;

/**
 * Check if a domain is whitelisted
 * @param {string} domain - Domain to check
 * @returns {Promise<boolean>} - True if whitelisted
 */
async function checkIfWhitelisted(domain) {
  try {
    const result = await chrome.storage.local.get('phishguard_whitelist');
    const whitelist = result.phishguard_whitelist || [];
    return whitelist.includes(domain);
  } catch (error) {
    console.error('Failed to check whitelist:', error);
    return false; // Default to not whitelisted if there's an error
  }
}

/**
 * Phishing keywords and patterns to detect
 */
const PHISHING_PATTERNS = {
  urgentKeywords: [
    'urgent', 'immediate', 'expires today', 'act now', 'limited time',
    'verify account', 'suspended', 'locked', 'unauthorized access',
    'click here immediately', 'confirm identity', 'update payment',
    'security alert', 'unusual activity', 'temporary hold'
  ],
  
  financialKeywords: [
    'bank account', 'credit card', 'social security', 'ssn', 'routing number',
    'account number', 'pin number', 'password', 'security code', 'cvv'
  ],
  
  actionKeywords: [
    'click here', 'download now', 'install', 'enable', 'activate',
    'verify now', 'confirm now', 'update now', 'sign in'
  ],
  
  impersonationKeywords: [
    'paypal', 'amazon', 'apple', 'microsoft', 'google', 'facebook',
    'bank of america', 'chase', 'wells fargo', 'irs', 'fedex', 'ups'
  ]
};

/**
 * Initialize content script when DOM is ready
 */
function initializeContentScript() {
  console.log('PhishGuard content script initialized');
  
  // Perform initial page analysis
  analyzePageContent();
  
  // Set up observers for dynamic content
  setupMutationObserver();
  
  // Listen for messages from background script
  chrome.runtime.onMessage.addListener(handleBackgroundMessage);
  
  // Analyze forms when they're submitted
  document.addEventListener('submit', handleFormSubmission, true);
  
  // Monitor for new forms added dynamically
  document.addEventListener('DOMContentLoaded', analyzePageContent);
}

/**
 * Analyze page content for phishing indicators
 */
function analyzePageContent() {
  pageAnalysis = {
    phishingKeywords: [],
    insecureForms: [],
    suspiciousElements: [],
    threatLevel: 'safe'
  };
  
  // Analyze text content for phishing keywords
  analyzeTextContent();
  
  // Check forms for security issues
  analyzeForms();
  
  // Check page metadata
  analyzeMetadata();
  
  // Check for suspicious elements
  analyzeSuspiciousElements();
  
  // Determine overall threat level
  determineThreatLevel();
  
  console.log('Page analysis complete:', pageAnalysis);
}

/**
 * Analyze text content for phishing keywords
 */
function analyzeTextContent() {
  const textContent = document.body.textContent.toLowerCase();
  const foundKeywords = [];
  
  // Check each category of keywords
  Object.entries(PHISHING_PATTERNS).forEach(([category, keywords]) => {
    keywords.forEach(keyword => {
      if (textContent.includes(keyword.toLowerCase())) {
        foundKeywords.push({
          keyword,
          category,
          severity: getSeverityForCategory(category)
        });
      }
    });
  });
  
  pageAnalysis.phishingKeywords = foundKeywords;
}

/**
 * Get severity level for keyword category
 * @param {string} category - Keyword category
 * @returns {string} - Severity level
 */
function getSeverityForCategory(category) {
  const severityMap = {
    urgentKeywords: 'high',
    financialKeywords: 'critical',
    actionKeywords: 'medium',
    impersonationKeywords: 'high'
  };
  return severityMap[category] || 'low';
}

/**
 * Analyze forms for security issues
 */
function analyzeForms() {
  const forms = document.querySelectorAll('form');
  const insecureForms = [];
  
  forms.forEach(form => {
    const issues = [];
    
    // Check if form is submitted over HTTP instead of HTTPS
    if (window.location.protocol === 'http:') {
      issues.push('Form submitted over insecure HTTP connection');
    }
    
    // Check for password fields without proper security
    const passwordFields = form.querySelectorAll('input[type="password"]');
    if (passwordFields.length > 0 && window.location.protocol === 'http:') {
      issues.push('Password field on insecure page');
    }
    
    // Check for suspicious form actions
    const action = form.getAttribute('action');
    if (action && action.startsWith('http://')) {
      issues.push('Form submits to insecure HTTP URL');
    }
    
    // Check for forms requesting sensitive information
    const inputs = form.querySelectorAll('input');
    const sensitiveFields = [];
    
    inputs.forEach(input => {
      const name = (input.name || '').toLowerCase();
      const placeholder = (input.placeholder || '').toLowerCase();
      const label = getInputLabel(input);
      
      const sensitivePatterns = [
        'ssn', 'social security', 'credit card', 'card number',
        'cvv', 'security code', 'routing', 'account number'
      ];
      
      sensitivePatterns.forEach(pattern => {
        if (name.includes(pattern) || placeholder.includes(pattern) || 
            label.toLowerCase().includes(pattern)) {
          sensitiveFields.push(pattern);
        }
      });
    });
    
    if (sensitiveFields.length > 0) {
      issues.push(`Requests sensitive information: ${sensitiveFields.join(', ')}`);
    }
    
    if (issues.length > 0) {
      insecureForms.push({
        form,
        issues,
        sensitiveFields
      });
      
      // Highlight insecure forms
      form.classList.add('phishguard-insecure-form');
    }
  });
  
  pageAnalysis.insecureForms = insecureForms;
}

/**
 * Get label text for an input element
 * @param {HTMLElement} input - Input element
 * @returns {string} - Label text
 */
function getInputLabel(input) {
  // Check for associated label
  if (input.id) {
    const label = document.querySelector(`label[for="${input.id}"]`);
    if (label) return label.textContent;
  }
  
  // Check for parent label
  const parentLabel = input.closest('label');
  if (parentLabel) return parentLabel.textContent;
  
  // Check for nearby text
  const previousSibling = input.previousElementSibling;
  if (previousSibling && previousSibling.tagName === 'LABEL') {
    return previousSibling.textContent;
  }
  
  return '';
}

/**
 * Analyze page metadata for suspicious indicators
 */
function analyzeMetadata() {
  const suspiciousElements = [];
  
  // Check for hidden elements with suspicious content
  const hiddenElements = document.querySelectorAll('[style*="display:none"], [style*="visibility:hidden"]');
  hiddenElements.forEach(element => {
    const text = element.textContent.toLowerCase();
    if (text.includes('phishing') || text.includes('scam') || text.includes('fake')) {
      suspiciousElements.push({
        type: 'hidden_suspicious_text',
        element,
        content: text.substring(0, 100)
      });
    }
  });
  
  // Check for suspicious meta tags
  const metaTags = document.querySelectorAll('meta');
  metaTags.forEach(meta => {
    const content = (meta.getAttribute('content') || '').toLowerCase();
    if (content.includes('phishing') || content.includes('fake')) {
      suspiciousElements.push({
        type: 'suspicious_meta',
        element: meta,
        content
      });
    }
  });
  
  pageAnalysis.suspiciousElements = suspiciousElements;
}

/**
 * Analyze for other suspicious elements
 */
function analyzeSuspiciousElements() {
  // Check for suspicious iframes
  const iframes = document.querySelectorAll('iframe');
  iframes.forEach(iframe => {
    const src = iframe.src;
    if (src && (src.includes('data:') || src.includes('javascript:'))) {
      pageAnalysis.suspiciousElements.push({
        type: 'suspicious_iframe',
        element: iframe,
        src
      });
    }
  });
  
  // Check for suspicious links
  const links = document.querySelectorAll('a[href]');
  links.forEach(link => {
    const href = link.href;
    const text = link.textContent;
    
    // Check for misleading links
    if (href && text && !href.includes(text) && text.includes('http')) {
      pageAnalysis.suspiciousElements.push({
        type: 'misleading_link',
        element: link,
        href,
        text
      });
    }
  });
}

/**
 * Determine overall threat level based on analysis
 */
function determineThreatLevel() {
  let threatLevel = 'safe';
  
  // Check phishing keywords
  const criticalKeywords = pageAnalysis.phishingKeywords.filter(k => k.severity === 'critical');
  const highKeywords = pageAnalysis.phishingKeywords.filter(k => k.severity === 'high');
  
  if (criticalKeywords.length > 0 || pageAnalysis.insecureForms.length > 0) {
    threatLevel = 'dangerous';
  } else if (highKeywords.length > 1 || pageAnalysis.suspiciousElements.length > 2) {
    threatLevel = 'suspicious';
  } else if (pageAnalysis.phishingKeywords.length > 3) {
    threatLevel = 'suspicious';
  }
  
  pageAnalysis.threatLevel = threatLevel;
}

/**
 * Handle messages from background script
 * @param {object} message - Message from background
 * @param {object} sender - Message sender
 * @param {function} sendResponse - Response function
 */
function handleBackgroundMessage(message, _sender, sendResponse) {
  switch (message.type) {
    case 'SHOW_THREAT_NOTIFICATION':
      showThreatNotification(message.data);
      break;
      
    case 'GET_PAGE_ANALYSIS':
      sendResponse(pageAnalysis);
      break;
      
    case 'REANALYZE_PAGE':
      analyzePageContent();
      sendResponse(pageAnalysis);
      break;

    case 'CLEAR_THREAT_NOTIFICATIONS':
      clearAllThreatNotifications();
      break;
  }
}

/**
 * Clear all threat notifications and overlays
 */
function clearAllThreatNotifications() {
  // Remove any existing threat overlays
  const existingOverlays = document.querySelectorAll('.phishguard-threat-overlay, .phishguard-notification');
  existingOverlays.forEach(overlay => {
    overlay.remove();
  });

  // Clear any pending notification timeouts
  if (notificationTimeout) {
    clearTimeout(notificationTimeout);
    notificationTimeout = null;
  }

  console.log('PhishGuard: All threat notifications cleared');
}

/**
 * Check if protection is currently enabled
 * @returns {Promise<boolean>} - True if protection is enabled
 */
async function isProtectionEnabled() {
  try {
    const result = await chrome.storage.local.get('phishguard_protection_enabled');
    return result.phishguard_protection_enabled !== false; // Default to true
  } catch (error) {
    console.error('Failed to check protection state:', error);
    return true; // Default to enabled on error
  }
}

/**
 * Show threat notification overlay
 * @param {object} analysisData - Analysis data from background script
 */
async function showThreatNotification(analysisData) {
  // First, check if protection is enabled
  const protectionEnabled = await isProtectionEnabled();
  if (!protectionEnabled) {
    console.log('PhishGuard: Threat notification suppressed - protection is disabled');
    return;
  }

  // Check if the current domain is whitelisted
  const currentDomain = window.location.hostname;
  const isWhitelisted = await checkIfWhitelisted(currentDomain);

  // If whitelisted, don't show any threat notifications
  if (isWhitelisted) {
    console.log('PhishGuard: Threat notification suppressed - domain is whitelisted:', currentDomain);
    return;
  }

  // Don't show multiple notifications
  if (document.querySelector('.phishguard-notification') || document.querySelector('.phishguard-alert-overlay')) {
    return;
  }

  // Show full overlay for dangerous threats, notification for suspicious
  if (analysisData.threatLevel === 'dangerous') {
    showDangerousAlert(analysisData);
  } else if (analysisData.threatLevel === 'suspicious') {
    showSuspiciousNotification(analysisData);
  }
}

/**
 * Show dangerous threat overlay alert
 * @param {object} analysisData - Analysis data
 */
function showDangerousAlert(analysisData) {
  const overlay = createDangerousAlertOverlay(analysisData);
  document.body.appendChild(overlay);

  // Add event listeners for buttons
  addPhishGuardEventListeners(overlay);

  // Prevent scrolling while overlay is shown
  document.body.classList.add('phishguard-no-scroll');
}

/**
 * Show suspicious threat notification
 * @param {object} analysisData - Analysis data
 */
function showSuspiciousNotification(analysisData) {
  const notification = createNotificationElement(analysisData);
  document.body.appendChild(notification);

  // Add event listeners for buttons
  addPhishGuardEventListeners(notification);

  // Auto-hide after 10 seconds
  notificationTimeout = setTimeout(() => {
    hideNotification();
  }, 10000);
}

/**
 * Create dangerous alert overlay
 * @param {object} data - Analysis data
 * @returns {HTMLElement} - Overlay element
 */
function createDangerousAlertOverlay(data) {
  const overlay = document.createElement('div');
  overlay.className = 'phishguard-alert-overlay';

  const threatCount = data.threats.length;
  const primaryThreats = data.threats.slice(0, 3);
  const additionalThreats = Math.max(0, threatCount - 3);

  overlay.innerHTML = `
    <div class="phishguard-alert-content phishguard-alert-dangerous">
      <div class="phishguard-alert-header">
        <div class="phishguard-alert-icon">⚠️</div>
        <h2 class="phishguard-alert-title">Dangerous Website Detected</h2>
      </div>

      <p class="phishguard-alert-description">
        PhishGuard has detected ${threatCount} critical security threat${threatCount !== 1 ? 's' : ''} on this website.
        This site may be attempting to steal your personal information or install malware.
      </p>

      <div class="phishguard-alert-threats">
        <h4>Detected Threats:</h4>
        <ul>
          ${primaryThreats.map(threat => `<li>${escapeHtml(threat)}</li>`).join('')}
          ${additionalThreats > 0 ? `<li>+${additionalThreats} additional threat${additionalThreats !== 1 ? 's' : ''}</li>` : ''}
        </ul>
      </div>

      <div class="phishguard-alert-actions">
        <button class="phishguard-alert-button phishguard-alert-button-primary" data-action="go-back">
          Leave This Site
        </button>
        <button class="phishguard-alert-button phishguard-alert-button-secondary" data-action="proceed-anyway">
          Proceed Anyway (Not Recommended)
        </button>
      </div>
    </div>
  `;

  return overlay;
}

/**
 * Create notification element
 * @param {object} data - Analysis data
 * @returns {HTMLElement} - Notification element
 */
function createNotificationElement(data) {
  const notification = document.createElement('div');
  notification.className = `phishguard-notification phishguard-notification-${data.threatLevel}`;

  const title = data.threatLevel === 'suspicious' ? '⚠️ Suspicious Website Detected' :
                '✅ Website Appears Safe';

  notification.innerHTML = `
    <div class="phishguard-notification-header">
      <div class="phishguard-notification-title">${title}</div>
      <button class="phishguard-notification-close" data-action="dismiss">×</button>
    </div>
    <div class="phishguard-notification-message">
      ${data.threats.slice(0, 2).join('. ')}
      ${data.threats.length > 2 ? ` (+${data.threats.length - 2} more)` : ''}
    </div>
    ${data.threats.length > 2 ? `
      <div class="phishguard-show-more">
        <button class="phishguard-show-more-btn" data-action="show-details">
          Show all threats
        </button>
      </div>
    ` : ''}
  `;

  return notification;
}

/**
 * Hide notification
 */
function hideNotification() {
  const notification = document.querySelector('.phishguard-notification');
  if (notification) {
    notification.remove();
  }
  if (notificationTimeout) {
    clearTimeout(notificationTimeout);
    notificationTimeout = null;
  }
}

/**
 * Hide dangerous alert overlay
 */
function hideDangerousAlert() {
  const overlay = document.querySelector('.phishguard-alert-overlay');
  if (overlay) {
    overlay.remove();
    // Restore scrolling
    document.body.classList.remove('phishguard-no-scroll');
  }
}

/**
 * Escape HTML to prevent XSS
 * @param {string} text - Text to escape
 * @returns {string} - Escaped text
 */
function escapeHtml(text) {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

/**
 * Add event listeners to PhishGuard elements
 * @param {HTMLElement} container - Container element to search for buttons
 */
function addPhishGuardEventListeners(container) {
  const buttons = container.querySelectorAll('[data-action]');
  buttons.forEach(button => {
    const action = button.getAttribute('data-action');
    button.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();

      switch (action) {
        case 'go-back':
          phishGuardGoBack();
          break;
        case 'proceed-anyway':
          phishGuardProceedAnyway();
          break;
        case 'dismiss':
          phishGuardDismissNotification(button);
          break;
        case 'show-details':
          phishGuardShowDetails();
          break;
        case 'close':
          // Find the closest overlay or modal and remove it
          const overlay = button.closest('.phishguard-alert-overlay');
          if (overlay) {
            overlay.remove();
            // Restore scrolling if this was a dangerous alert
            document.body.classList.remove('phishguard-no-scroll');
          }
          break;
      }
    });
  });
}

// Functions for overlay buttons
function phishGuardGoBack() {
  // Try to go back in history, or close tab if possible
  if (window.history.length > 1) {
    window.history.back();
  } else {
    // If we can't go back, try to navigate to a safe page
    window.location.href = 'about:blank';
  }
  hideDangerousAlert();
}

function phishGuardProceedAnyway() {
  // Log that user chose to proceed despite warning
  console.warn('PhishGuard: User chose to proceed despite dangerous threat warning');

  // Send message to background script to log this decision
  chrome.runtime.sendMessage({
    type: 'USER_PROCEEDED_DESPITE_WARNING',
    url: window.location.href,
    timestamp: Date.now()
  });

  hideDangerousAlert();
}

function phishGuardDismissNotification(element) {
  if (element && element.closest('.phishguard-notification')) {
    element.closest('.phishguard-notification').remove();
  }
  hideNotification();
}

// Enhanced notification with better user interaction
function phishGuardShowDetails() {
  // Show more detailed threat information
  const notification = document.querySelector('.phishguard-notification');
  if (notification) {
    // Replace notification with expanded version
    notification.remove();

    // Get current analysis data and show detailed popup
    chrome.runtime.sendMessage({ type: 'GET_CURRENT_ANALYSIS' }, (response) => {
      if (response) {
        showDetailedThreatInfo(response);
      }
    });
  }
}

/**
 * Show detailed threat information in a modal
 * @param {object} analysisData - Analysis data
 */
function showDetailedThreatInfo(analysisData) {
  const modal = document.createElement('div');
  modal.className = 'phishguard-alert-overlay';

  modal.innerHTML = `
    <div class="phishguard-alert-content phishguard-alert-${analysisData.threatLevel}">
      <div class="phishguard-alert-header">
        <div class="phishguard-alert-icon">${analysisData.threatLevel === 'dangerous' ? '🚨' : '⚠️'}</div>
        <h2 class="phishguard-alert-title">Threat Details</h2>
      </div>

      <p class="phishguard-alert-description">
        Website: ${escapeHtml(analysisData.domain || analysisData.url)}
      </p>

      <div class="phishguard-alert-threats">
        <h4>All Detected Issues:</h4>
        <ul>
          ${analysisData.threats.map(threat => `<li>${escapeHtml(threat)}</li>`).join('')}
        </ul>
      </div>

      <div class="phishguard-alert-actions">
        <button class="phishguard-alert-button phishguard-alert-button-secondary" data-action="close">
          Close
        </button>
      </div>
    </div>
  `;

  document.body.appendChild(modal);

  // Add event listeners for buttons
  addPhishGuardEventListeners(modal);
}

/**
 * Handle form submission to warn about insecure forms
 * @param {Event} event - Submit event
 */
async function handleFormSubmission(event) {
  const form = event.target;

  // First, check if the current domain is whitelisted
  const currentDomain = window.location.hostname;
  const isWhitelisted = await checkIfWhitelisted(currentDomain);

  // If whitelisted, allow form submission without any interference
  if (isWhitelisted) {
    console.log('PhishGuard: Form submission allowed - domain is whitelisted:', currentDomain);
    return; // Allow form to submit normally
  }

  const insecureForm = pageAnalysis.insecureForms.find(f => f.form === form);

  // Check if this is a dangerous site
  const isDangerousSite = pageAnalysis.threatLevel === 'dangerous';

  if (isDangerousSite || (insecureForm && insecureForm.issues.length > 0)) {
    event.preventDefault();
    event.stopPropagation();

    let warningMessage;
    if (isDangerousSite) {
      warningMessage = 'PhishGuard has blocked form submission on this dangerous website. This site may be attempting to steal your information.';
      showFormBlockedAlert(form, warningMessage);
    } else {
      warningMessage = `PhishGuard Warning: This form has security issues:\n\n${insecureForm.issues.join('\n')}\n\nDo you want to proceed?`;
      const proceed = confirm(warningMessage);

      if (proceed) {
        // Allow form submission if user confirms
        form.removeEventListener('submit', handleFormSubmission);
        form.submit();
      }
    }
  }
}

/**
 * Show form blocked alert
 * @param {HTMLElement} form - The blocked form
 * @param {string} message - Warning message
 */
function showFormBlockedAlert(form, message) {
  // Create a warning overlay specifically for the form
  const overlay = document.createElement('div');
  overlay.className = 'phishguard-alert-overlay';

  overlay.innerHTML = `
    <div class="phishguard-alert-content phishguard-alert-dangerous">
      <div class="phishguard-alert-header">
        <div class="phishguard-alert-icon">🛡️</div>
        <h2 class="phishguard-alert-title">Form Submission Blocked</h2>
      </div>

      <p class="phishguard-alert-description">
        ${escapeHtml(message)}
      </p>

      <div class="phishguard-alert-actions">
        <button class="phishguard-alert-button phishguard-alert-button-primary" data-action="go-back">
          Leave This Site
        </button>
        <button class="phishguard-alert-button phishguard-alert-button-secondary" data-action="close">
          Stay on Page
        </button>
      </div>
    </div>
  `;

  document.body.appendChild(overlay);

  // Add event listeners for buttons
  addPhishGuardEventListeners(overlay);

  // Highlight the blocked form
  form.classList.add('phishguard-blocked-form');
}

/**
 * Set up mutation observer for dynamic content
 */
function setupMutationObserver() {
  const observer = new MutationObserver((mutations) => {
    let shouldReanalyze = false;
    
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        // Check if new forms or suspicious elements were added
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            if (node.tagName === 'FORM' || node.querySelector('form')) {
              shouldReanalyze = true;
            }
          }
        });
      }
    });
    
    if (shouldReanalyze) {
      // Debounce reanalysis
      setTimeout(analyzePageContent, 1000);
    }
  });
  
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
}

// Initialize when script loads
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeContentScript);
} else {
  initializeContentScript();
}

console.log('PhishGuard content script loaded');
