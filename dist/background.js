/**
 * PhishGuard Background Service Worker
 * Handles URL analysis, API calls, and navigation monitoring
 */

// Inline utilities for Manifest V3 compatibility (importScripts not supported)
// CONFIG object from utils.js
const CONFIG = {
  THREAT_LEVELS: {
    SAFE: 'safe',
    SUSPICIOUS: 'suspicious',
    DANGEROUS: 'dangerous'
  },
  STORAGE_KEYS: {
    SCAN_HISTORY: 'phishguard_scan_history',
    SETTINGS: 'phishguard_settings',
    WHITELIST: 'phishguard_whitelist'
  }
};

// Error types from error-handler.js
const ERROR_TYPES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  API_ERROR: 'API_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  STORAGE_ERROR: 'STORAGE_ERROR',
  PERMISSION_ERROR: 'PERMISSION_ERROR'
};

// Basic error wrapper function
function asyncErrorWrapper(fn, context = 'unknown') {
  return async function(...args) {
    try {
      return await fn.apply(this, args);
    } catch (error) {
      console.error(`Error in ${context}:`, error);
      throw error;
    }
  };
}

// Performance monitor stub
const performanceMonitor = {
  startTimer: (name) => {},
  endTimer: (name) => {}
};

// Basic utility functions
function isValidUrl(url) {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

function extractDomain(url) {
  try {
    return new URL(url).hostname;
  } catch {
    return '';
  }
}

// Basic domain analysis
function analyzeDomainPatterns(domain) {
  const threats = [];
  let threatLevel = CONFIG.THREAT_LEVELS.SAFE;

  // Check for suspicious patterns
  if (domain.includes('secure-') || domain.includes('-secure')) {
    threats.push('Suspicious security-themed domain name');
    threatLevel = CONFIG.THREAT_LEVELS.SUSPICIOUS;
  }

  if (domain.match(/[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}/)) {
    threats.push('Domain uses IP address instead of domain name');
    threatLevel = CONFIG.THREAT_LEVELS.SUSPICIOUS;
  }

  return { threats, threatLevel };
}

/**
 * Check URL against Google Safe Browsing API
 * @param {string} url - URL to check
 * @param {string} apiKey - User's Google Safe Browsing API key
 * @returns {Promise<object>} - Safe Browsing API response
 */
async function checkSafeBrowsing(url, apiKey) {
  if (!apiKey || !url) {
    return { matches: [] };
  }

  try {
    const apiUrl = `https://safebrowsing.googleapis.com/v4/threatMatches:find?key=${apiKey}`;

    const requestBody = {
      client: {
        clientId: "phishguard-extension",
        clientVersion: "1.0.0"
      },
      threatInfo: {
        threatTypes: [
          "MALWARE",
          "SOCIAL_ENGINEERING",
          "UNWANTED_SOFTWARE",
          "POTENTIALLY_HARMFUL_APPLICATION"
        ],
        platformTypes: ["ANY_PLATFORM"],
        threatEntryTypes: ["URL"],
        threatEntries: [{ url: url }]
      }
    };

    console.log('PhishGuard: Checking URL with Safe Browsing API:', url);

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Safe Browsing API error:', response.status, errorText);

      // Handle specific error cases
      if (response.status === 400) {
        throw new Error('Invalid API request - check your API key and URL format');
      } else if (response.status === 403) {
        throw new Error('API key invalid or quota exceeded');
      } else if (response.status === 429) {
        throw new Error('API rate limit exceeded');
      } else {
        throw new Error(`Safe Browsing API error: ${response.status}`);
      }
    }

    const result = await response.json();
    console.log('PhishGuard: Safe Browsing API response:', result);

    return result;

  } catch (error) {
    console.error('Safe Browsing API check failed:', error);
    throw error;
  }
}

// Storage helper
async function storeScanResult(result) {
  try {
    const existing = await chrome.storage.local.get(CONFIG.STORAGE_KEYS.SCAN_HISTORY);
    const history = existing[CONFIG.STORAGE_KEYS.SCAN_HISTORY] || [];
    history.unshift(result);

    // Keep only last 100 results
    if (history.length > 100) {
      history.splice(100);
    }

    await chrome.storage.local.set({
      [CONFIG.STORAGE_KEYS.SCAN_HISTORY]: history
    });
  } catch (error) {
    console.error('Failed to store scan result:', error);
  }
}

// Global state
let currentTabAnalysis = new Map();
let sslCertificateInfo = new Map();
let apiKeys = {
  safeBrowsing: null,
  phishTank: null
};

/**
 * Initialize extension on startup
 */
chrome.runtime.onStartup.addListener(async () => {
  console.log('PhishGuard extension started');
  await loadSettings();
});

chrome.runtime.onInstalled.addListener(async (details) => {
  console.log('PhishGuard extension installed/updated');

  if (details.reason === 'install') {
    // Set default settings on first install
    await setDefaultSettings();

    // Set up periodic cleanup alarm
    chrome.alarms.create('cleanupHistory', {
      delayInMinutes: 60, // First cleanup after 1 hour
      periodInMinutes: 24 * 60 // Then every 24 hours
    });
  }

  await loadSettings();
});

/**
 * Handle periodic cleanup alarm
 */
chrome.alarms.onAlarm.addListener(async (alarm) => {
  if (alarm.name === 'cleanupHistory') {
    try {
      // Clean up history older than 30 days
      const removedCount = await clearOldScanHistory(30);
      console.log(`Cleaned up ${removedCount} old scan history entries`);
    } catch (error) {
      console.error('Failed to clean up scan history:', error);
    }
  }
});

/**
 * Load extension settings from storage
 */
async function loadSettings() {
  try {
    const settings = await chrome.storage.local.get(CONFIG.STORAGE_KEYS.SETTINGS);
    const userSettings = settings[CONFIG.STORAGE_KEYS.SETTINGS] || {};
    
    apiKeys.safeBrowsing = userSettings.safeBrowsingApiKey || null;
    apiKeys.phishTank = userSettings.phishTankApiKey || null;
    
    console.log('Settings loaded:', { 
      hasSafeBrowsingKey: !!apiKeys.safeBrowsing,
      hasPhishTankKey: !!apiKeys.phishTank 
    });
  } catch (error) {
    console.error('Failed to load settings:', error);
  }
}

/**
 * Set default settings on first install
 */
async function setDefaultSettings() {
  const defaultSettings = {
    enableRealTimeScanning: true,
    enableSafeBrowsing: true,
    enableDomainAnalysis: true,
    enableSSLChecking: true,
    showNotifications: true,
    alertLevel: 'suspicious' // 'safe', 'suspicious', 'dangerous'
  };
  
  await chrome.storage.local.set({
    [CONFIG.STORAGE_KEYS.SETTINGS]: defaultSettings
  });
}

/**
 * Monitor SSL certificate information
 */
chrome.webRequest.onHeadersReceived.addListener(
  (details) => {
    if (details.frameId === 0 && details.url.startsWith('https://')) {
      // Store SSL information for later analysis
      const tabId = details.tabId;
      const url = details.url;
      const domain = extractDomain(url);

      // Extract SSL-related headers
      const sslInfo = {
        url,
        domain,
        hasSSL: true,
        timestamp: Date.now(),
        headers: details.responseHeaders || []
      };

      // Check for security headers
      const securityHeaders = analyzeSecurityHeaders(details.responseHeaders || []);
      sslInfo.securityHeaders = securityHeaders;

      sslCertificateInfo.set(tabId, sslInfo);
    }
  },
  { urls: ['https://*/*'] },
  ['responseHeaders']
);

/**
 * Monitor HTTP requests to detect insecure connections
 */
chrome.webRequest.onBeforeRequest.addListener(
  (details) => {
    if (details.frameId === 0 && details.url.startsWith('http://')) {
      const tabId = details.tabId;
      const url = details.url;
      const domain = extractDomain(url);

      // Store information about insecure connection
      const sslInfo = {
        url,
        domain,
        hasSSL: false,
        timestamp: Date.now(),
        insecureConnection: true
      };

      sslCertificateInfo.set(tabId, sslInfo);
    }
  },
  { urls: ['http://*/*'] }
);

/**
 * Analyze security headers
 * @param {Array} headers - Response headers
 * @returns {object} - Security headers analysis
 */
function analyzeSecurityHeaders(headers) {
  const securityHeaders = {
    hasHSTS: false,
    hasCSP: false,
    hasXFrameOptions: false,
    hasXContentTypeOptions: false,
    hasReferrerPolicy: false,
    issues: []
  };

  headers.forEach(header => {
    const name = header.name.toLowerCase();
    const value = header.value;

    switch (name) {
      case 'strict-transport-security':
        securityHeaders.hasHSTS = true;
        securityHeaders.hstsValue = value;
        break;
      case 'content-security-policy':
        securityHeaders.hasCSP = true;
        securityHeaders.cspValue = value;
        break;
      case 'x-frame-options':
        securityHeaders.hasXFrameOptions = true;
        securityHeaders.xFrameOptionsValue = value;
        break;
      case 'x-content-type-options':
        securityHeaders.hasXContentTypeOptions = true;
        break;
      case 'referrer-policy':
        securityHeaders.hasReferrerPolicy = true;
        break;
    }
  });

  // Identify missing security headers
  if (!securityHeaders.hasHSTS) {
    securityHeaders.issues.push('Missing HSTS header');
  }
  if (!securityHeaders.hasCSP) {
    securityHeaders.issues.push('Missing Content Security Policy');
  }
  if (!securityHeaders.hasXFrameOptions) {
    securityHeaders.issues.push('Missing X-Frame-Options header');
  }
  if (!securityHeaders.hasXContentTypeOptions) {
    securityHeaders.issues.push('Missing X-Content-Type-Options header');
  }

  return securityHeaders;
}

/**
 * Monitor navigation events for real-time analysis
 */
chrome.webNavigation.onBeforeNavigate.addListener(async (details) => {
  // Only analyze main frame navigations
  if (details.frameId !== 0) return;
  
  const tabId = details.tabId;
  const url = details.url;
  
  // Skip chrome:// and extension:// URLs
  if (url.startsWith('chrome://') || url.startsWith('chrome-extension://') || url.startsWith('moz-extension://')) {
    return;
  }
  
  console.log('Analyzing navigation to:', url);
  
  try {
    const analysis = await analyzeUrl(url);
    currentTabAnalysis.set(tabId, analysis);
    
    // Update extension badge based on threat level
    await updateBadge(tabId, analysis.threatLevel);
    
    // Show notification if threat detected
    if (analysis.threatLevel !== CONFIG.THREAT_LEVELS.SAFE) {
      await showThreatNotification(tabId, analysis);
    }
    
    // Store analysis result
    await storeScanResult({
      url,
      domain: extractDomain(url),
      threatLevel: analysis.threatLevel,
      threats: analysis.threats,
      timestamp: Date.now()
    });
    
  } catch (error) {
    console.error('URL analysis failed:', error);
    currentTabAnalysis.set(tabId, {
      url,
      threatLevel: CONFIG.THREAT_LEVELS.SAFE,
      threats: ['Analysis failed: ' + error.message],
      error: true
    });
  }
});

/**
 * Comprehensive URL analysis
 * @param {string} url - URL to analyze
 * @returns {Promise<object>} - Analysis result
 */
const analyzeUrl = asyncErrorWrapper(async function(url) {
  performanceMonitor.startTimer('url_analysis');

  if (!isValidUrl(url)) {
    throw new PhishGuardError('Invalid URL provided', ERROR_TYPES.VALIDATION_ERROR, { url });
  }

  const domain = extractDomain(url);
  const analysis = {
    url,
    domain,
    threatLevel: CONFIG.THREAT_LEVELS.SAFE,
    threats: [],
    sources: []
  };
  
  // 1. Domain pattern analysis (always runs)
  const domainAnalysis = analyzeDomainPatterns(domain);
  if (domainAnalysis.threats.length > 0) {
    analysis.threats.push(...domainAnalysis.threats);
    analysis.threatLevel = domainAnalysis.threatLevel;
    analysis.sources.push('Domain Pattern Analysis');
  }
  
  // 2. Google Safe Browsing API check
  if (apiKeys.safeBrowsing) {
    try {
      const safeBrowsingResult = await checkSafeBrowsing(url, apiKeys.safeBrowsing);
      if (safeBrowsingResult.matches && safeBrowsingResult.matches.length > 0) {
        // Process each threat match
        safeBrowsingResult.matches.forEach(match => {
          const threatType = match.threatType;
          const platformType = match.platformType || 'ANY_PLATFORM';

          // Convert threat types to user-friendly messages
          let threatMessage;
          switch (threatType) {
            case 'MALWARE':
              threatMessage = 'Contains malware that could harm your device';
              break;
            case 'SOCIAL_ENGINEERING':
              threatMessage = 'Suspected phishing site attempting to steal personal information';
              break;
            case 'UNWANTED_SOFTWARE':
              threatMessage = 'May download unwanted or potentially harmful software';
              break;
            case 'POTENTIALLY_HARMFUL_APPLICATION':
              threatMessage = 'Contains potentially harmful applications';
              break;
            default:
              threatMessage = `Flagged for: ${threatType}`;
          }

          analysis.threats.push(`Google Safe Browsing: ${threatMessage}`);
        });

        // Set threat level to dangerous for any Safe Browsing match
        analysis.threatLevel = CONFIG.THREAT_LEVELS.DANGEROUS;
        analysis.sources.push('Google Safe Browsing API');

        console.log('PhishGuard: Safe Browsing threats detected:', safeBrowsingResult.matches);
      } else {
        console.log('PhishGuard: No Safe Browsing threats detected for:', url);
      }
    } catch (error) {
      console.warn('Safe Browsing API check failed:', error.message);
      // Don't add error to threats list unless it's a critical API issue
      if (error.message.includes('API key invalid')) {
        analysis.threats.push('Safe Browsing API: Invalid API key');
      } else if (error.message.includes('quota exceeded')) {
        analysis.threats.push('Safe Browsing API: Daily quota exceeded');
      }
      // For other errors, just log them without affecting the user experience
    }
  }
  
  // 3. SSL Certificate Analysis
  const sslInfo = sslCertificateInfo.get(getCurrentTabId());
  if (sslInfo) {
    const sslAnalysis = analyzeSSLSecurity(sslInfo);
    if (sslAnalysis.threats.length > 0) {
      analysis.threats.push(...sslAnalysis.threats);
      if (sslAnalysis.threatLevel === CONFIG.THREAT_LEVELS.DANGEROUS) {
        analysis.threatLevel = CONFIG.THREAT_LEVELS.DANGEROUS;
      } else if (sslAnalysis.threatLevel === CONFIG.THREAT_LEVELS.SUSPICIOUS &&
                 analysis.threatLevel === CONFIG.THREAT_LEVELS.SAFE) {
        analysis.threatLevel = CONFIG.THREAT_LEVELS.SUSPICIOUS;
      }
      analysis.sources.push('SSL Certificate Analysis');
    }
    analysis.sslInfo = sslInfo;
  }

  // 4. Check against local whitelist
  const whitelist = await getWhitelist();
  if (whitelist.includes(domain)) {
    analysis.threatLevel = CONFIG.THREAT_LEVELS.SAFE;
    analysis.threats = analysis.threats.filter(threat => !threat.includes('whitelisted'));
    analysis.threats.unshift('Domain is whitelisted');
    analysis.sources.push('Local Whitelist');
  }

  performanceMonitor.endTimer('url_analysis');
  return analysis;
}, 'url_analysis');

/**
 * Analyze SSL security information
 * @param {object} sslInfo - SSL information
 * @returns {object} - SSL analysis result
 */
function analyzeSSLSecurity(sslInfo) {
  const analysis = {
    threatLevel: CONFIG.THREAT_LEVELS.SAFE,
    threats: []
  };

  // Check for insecure HTTP connection
  if (!sslInfo.hasSSL) {
    analysis.threats.push('Website uses insecure HTTP connection');
    analysis.threatLevel = CONFIG.THREAT_LEVELS.DANGEROUS;
    return analysis;
  }

  // Analyze security headers
  if (sslInfo.securityHeaders) {
    const headerIssues = sslInfo.securityHeaders.issues;

    if (headerIssues.length > 0) {
      // Only report critical missing headers as threats
      const criticalIssues = headerIssues.filter(issue =>
        issue.includes('HSTS') || issue.includes('Content Security Policy')
      );

      if (criticalIssues.length > 0) {
        analysis.threats.push(`Missing security headers: ${criticalIssues.join(', ')}`);
        analysis.threatLevel = CONFIG.THREAT_LEVELS.SUSPICIOUS;
      }
    }
  }

  return analysis;
}

/**
 * Get current tab ID (helper function)
 * @returns {number|null} - Current tab ID
 */
function getCurrentTabId() {
  // This is a simplified approach - in practice, you'd need to track the current tab
  // For now, we'll use the most recent tab that was analyzed
  const entries = Array.from(currentTabAnalysis.entries());
  return entries.length > 0 ? entries[entries.length - 1][0] : null;
}

/**
 * Update extension badge based on threat level
 * @param {number} tabId - Tab ID
 * @param {string} threatLevel - Threat level
 */
async function updateBadge(tabId, threatLevel) {
  const badgeConfig = {
    [CONFIG.THREAT_LEVELS.SAFE]: { text: '', color: '#10b981' },
    [CONFIG.THREAT_LEVELS.SUSPICIOUS]: { text: '!', color: '#f59e0b' },
    [CONFIG.THREAT_LEVELS.DANGEROUS]: { text: '⚠', color: '#ef4444' }
  };
  
  const config = badgeConfig[threatLevel] || badgeConfig[CONFIG.THREAT_LEVELS.SAFE];
  
  await chrome.action.setBadgeText({ tabId, text: config.text });
  await chrome.action.setBadgeBackgroundColor({ tabId, color: config.color });
}

/**
 * Show threat notification to content script
 * @param {number} tabId - Tab ID
 * @param {object} analysis - Analysis result
 */
async function showThreatNotification(tabId, analysis) {
  try {
    await chrome.tabs.sendMessage(tabId, {
      type: 'SHOW_THREAT_NOTIFICATION',
      data: analysis
    });
  } catch (error) {
    // Content script might not be ready yet, ignore error
    console.log('Could not send notification to content script:', error.message);
  }
}

/**
 * Test Safe Browsing API key with a known safe URL
 * @param {string} testUrl - URL to test with
 * @param {string} apiKey - API key to test
 * @returns {Promise<object>} - Test result
 */
async function testSafeBrowsingApiKey(testUrl, apiKey) {
  try {
    console.log('PhishGuard: Testing Safe Browsing API key...');

    const result = await checkSafeBrowsing(testUrl, apiKey);

    // If we get here without an error, the API key is working
    return {
      success: true,
      message: 'API key is valid and working',
      hasMatches: result.matches && result.matches.length > 0
    };

  } catch (error) {
    console.error('Safe Browsing API test failed:', error);

    // Return specific error messages based on the error
    let errorMessage = error.message;
    if (errorMessage.includes('403')) {
      errorMessage = 'Invalid API key or access denied';
    } else if (errorMessage.includes('400')) {
      errorMessage = 'Invalid request format';
    } else if (errorMessage.includes('429')) {
      errorMessage = 'Rate limit exceeded';
    } else if (errorMessage.includes('quota exceeded')) {
      errorMessage = 'Daily quota exceeded';
    }

    return {
      success: false,
      error: errorMessage
    };
  }
}

/**
 * Get whitelist from storage
 * @returns {Promise<Array>} - Array of whitelisted domains
 */
async function getWhitelist() {
  try {
    const result = await chrome.storage.local.get(CONFIG.STORAGE_KEYS.WHITELIST);
    return result[CONFIG.STORAGE_KEYS.WHITELIST] || [];
  } catch (error) {
    console.error('Failed to get whitelist:', error);
    return [];
  }
}

/**
 * Handle messages from popup and content scripts
 */
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  switch (message.type) {
    case 'GET_CURRENT_ANALYSIS':
      const analysis = currentTabAnalysis.get(sender.tab?.id) || null;
      // Include SSL information if available
      if (analysis && sender.tab?.id) {
        const sslInfo = sslCertificateInfo.get(sender.tab.id);
        if (sslInfo) {
          analysis.sslInfo = sslInfo;
        }
      }
      sendResponse(analysis);
      break;
      
    case 'REANALYZE_CURRENT_TAB':
      const tabId = message.tabId || sender.tab?.id;
      const url = message.url || sender.tab?.url;

      if (tabId && url) {
        analyzeUrl(url).then(analysis => {
          currentTabAnalysis.set(tabId, analysis);
          sendResponse(analysis);
        }).catch(error => {
          console.error('Analysis failed:', error);
          sendResponse({ error: error.message });
        });
        return true; // Indicates async response
      } else {
        sendResponse({ error: 'No tab information available' });
      }
      break;
      
    case 'ADD_TO_WHITELIST':
      addToWhitelist(message.domain).then(() => {
        sendResponse({ success: true });
      }).catch(error => {
        sendResponse({ error: error.message });
      });
      return true;
      
    case 'REMOVE_FROM_WHITELIST':
      removeFromWhitelist(message.domain).then(() => {
        sendResponse({ success: true });
      }).catch(error => {
        sendResponse({ error: error.message });
      });
      return true;
      
    case 'UPDATE_SETTINGS':
      updateSettings(message.settings).then(() => {
        sendResponse({ success: true });
      }).catch(error => {
        sendResponse({ error: error.message });
      });
      return true;

    case 'TEST_SAFE_BROWSING_API':
      testSafeBrowsingApiKey(message.url, message.apiKey).then(result => {
        sendResponse(result);
      }).catch(error => {
        sendResponse({ success: false, error: error.message });
      });
      return true;
  }
});

/**
 * Add domain to whitelist
 * @param {string} domain - Domain to whitelist
 */
async function addToWhitelist(domain) {
  const whitelist = await getWhitelist();
  if (!whitelist.includes(domain)) {
    whitelist.push(domain);
    await chrome.storage.local.set({
      [CONFIG.STORAGE_KEYS.WHITELIST]: whitelist
    });
  }
}

/**
 * Remove domain from whitelist
 * @param {string} domain - Domain to remove
 */
async function removeFromWhitelist(domain) {
  const whitelist = await getWhitelist();
  const filtered = whitelist.filter(d => d !== domain);
  await chrome.storage.local.set({
    [CONFIG.STORAGE_KEYS.WHITELIST]: filtered
  });
}

/**
 * Update extension settings
 * @param {object} newSettings - New settings to apply
 */
async function updateSettings(newSettings) {
  const currentSettings = await chrome.storage.local.get(CONFIG.STORAGE_KEYS.SETTINGS);
  const settings = { ...currentSettings[CONFIG.STORAGE_KEYS.SETTINGS], ...newSettings };

  await chrome.storage.local.set({
    [CONFIG.STORAGE_KEYS.SETTINGS]: settings
  });

  // Update API keys if provided
  if (newSettings.safeBrowsingApiKey !== undefined) {
    apiKeys.safeBrowsing = newSettings.safeBrowsingApiKey;
  }
  if (newSettings.phishTankApiKey !== undefined) {
    apiKeys.phishTank = newSettings.phishTankApiKey;
  }
}

/**
 * Clear old scan history entries
 * @param {number} daysToKeep - Number of days of history to keep
 * @returns {Promise<number>} - Number of entries removed
 */
async function clearOldScanHistory(daysToKeep = 30) {
  try {
    const result = await chrome.storage.local.get(CONFIG.STORAGE_KEYS.SCAN_HISTORY);
    const history = result[CONFIG.STORAGE_KEYS.SCAN_HISTORY] || [];
    const cutoffTime = Date.now() - (daysToKeep * 24 * 60 * 60 * 1000);

    const filteredHistory = history.filter(scan => scan.timestamp >= cutoffTime);
    const removedCount = history.length - filteredHistory.length;

    if (removedCount > 0) {
      await chrome.storage.local.set({
        [CONFIG.STORAGE_KEYS.SCAN_HISTORY]: filteredHistory
      });
    }

    return removedCount;
  } catch (error) {
    console.error('Failed to clear old scan history:', error);
    return 0;
  }
}

/**
 * Clean up when tab is closed
 */
chrome.tabs.onRemoved.addListener((tabId) => {
  currentTabAnalysis.delete(tabId);
  sslCertificateInfo.delete(tabId);
});

console.log('PhishGuard background script loaded');
