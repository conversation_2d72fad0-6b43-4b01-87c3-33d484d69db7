<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PhishGuard</title>
    <link rel="stylesheet" href="tailwind-local.css">

</head>
<body class="bg-gray-50">
    <!-- Header -->
    <div class="bg-white border-b border-gray-200 px-4 py-3">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
                <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                    <span class="text-white font-bold text-sm">PG</span>
                </div>
                <h1 class="text-lg font-semibold text-gray-900">PhishGuard</h1>
            </div>
            <button id="settingsBtn" class="p-1 rounded-md hover:bg-gray-100 transition-colors">
                <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
            </button>
        </div>
    </div>

    <!-- Protection Toggle -->
    <div class="bg-white border-b border-gray-200 px-4 py-3">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
                <div class="flex items-center">
                    <div class="w-3 h-3 rounded-full bg-green-500" id="protectionStatusDot"></div>
                </div>
                <div>
                    <div class="text-sm font-medium text-gray-900" id="protectionStatusText">Protection Enabled</div>
                    <div class="text-xs text-gray-500">All PhishGuard features active</div>
                </div>
            </div>
            <div class="flex items-center space-x-2">
                <span class="text-xs text-gray-500" id="toggleLabel">ON</span>
                <button id="protectionToggle" class="relative inline-flex h-6 w-11 items-center rounded-full bg-green-600 transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2" role="switch" aria-checked="true">
                    <span class="sr-only">Toggle PhishGuard protection</span>
                    <span id="toggleSlider" class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-6"></span>
                </button>
            </div>
        </div>
        <div id="protectionTooltip" class="mt-2 text-xs text-gray-600 hidden">
            <span id="tooltipText">PhishGuard is actively protecting you from malicious websites, phishing attempts, and unsafe forms.</span>
        </div>
    </div>

    <!-- Loading State -->
    <div id="loadingState" class="flex items-center justify-center py-12">
        <div class="text-center">
            <div class="loading-spinner w-8 h-8 border-4 border-blue-200 border-t-blue-600 rounded-full mx-auto mb-3"></div>
            <p class="text-gray-600">Analyzing website...</p>
        </div>
    </div>

    <!-- Main Content -->
    <div id="mainContent" class="hidden">
        <!-- Threat Level Indicator -->
        <div id="threatIndicator" class="threat-indicator threat-safe text-white px-4 py-6">
            <div class="flex items-center justify-between">
                <div>
                    <div class="flex items-center space-x-2 mb-1">
                        <span id="threatIcon" class="text-2xl">🛡️</span>
                        <h2 id="threatTitle" class="text-xl font-bold">Safe Website</h2>
                    </div>
                    <p id="threatDescription" class="text-sm opacity-90">No threats detected</p>
                </div>
                <button id="refreshBtn" class="p-2 rounded-lg bg-white bg-opacity-20 hover:bg-opacity-30 transition-colors">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Website Info -->
        <div class="px-4 py-3 bg-white border-b border-gray-200">
            <div class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                    <div id="sslIndicator" class="w-3 h-3 rounded-full bg-green-500"></div>
                </div>
                <div class="flex-1 min-w-0">
                    <p id="currentUrl" class="text-sm font-medium text-gray-900 truncate">Loading...</p>
                    <p id="domainInfo" class="text-xs text-gray-500">Checking domain...</p>
                </div>
            </div>
        </div>

        <!-- Threat Details -->
        <div id="threatDetails" class="px-4 py-3 space-y-3">
            <!-- Threats List -->
            <div id="threatsSection" class="hidden">
                <h3 class="text-sm font-semibold text-gray-900 mb-2">Detected Threats</h3>
                <ul id="threatsList" class="space-y-2"></ul>
            </div>

            <!-- Page Analysis -->
            <div id="pageAnalysisSection">
                <h3 class="text-sm font-semibold text-gray-900 mb-2">Page Analysis</h3>
                <div class="grid grid-cols-2 gap-3">
                    <div class="bg-gray-50 rounded-lg p-3">
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-gray-600">Keywords</span>
                            <span id="keywordCount" class="text-sm font-semibold text-gray-900">0</span>
                        </div>
                    </div>
                    <div class="bg-gray-50 rounded-lg p-3">
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-gray-600">Forms</span>
                            <span id="formCount" class="text-sm font-semibold text-gray-900">0</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="space-y-2">
                <button id="whitelistBtn" class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors text-sm">
                    Add to Whitelist
                </button>
                <div class="grid grid-cols-2 gap-2">
                    <button id="reportBtn" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-3 rounded-lg transition-colors text-xs">
                        Report False Positive
                    </button>
                    <button id="reportThreatBtn" class="bg-orange-600 hover:bg-orange-700 text-white font-medium py-2 px-3 rounded-lg transition-colors text-xs">
                        Report New Threat
                    </button>
                </div>
                <button id="feedbackBtn" class="w-full bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors text-sm">
                    Send Feedback
                </button>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="px-4 py-3 border-t border-gray-200">
            <div class="flex items-center justify-between mb-2">
                <h3 class="text-sm font-semibold text-gray-900">Recent Activity</h3>
                <button id="viewHistoryBtn" class="text-xs text-blue-600 hover:text-blue-700">View All</button>
            </div>
            <div id="recentActivity" class="space-y-2">
                <p class="text-xs text-gray-500">No recent activity</p>
            </div>
        </div>
    </div>

    <!-- Settings Panel -->
    <div id="settingsPanel" class="hidden">
        <div class="px-4 py-3 bg-white border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-semibold text-gray-900">Settings</h2>
                <button id="backBtn" class="p-1 rounded-md hover:bg-gray-100 transition-colors">
                    <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>

        <div class="px-4 py-3 space-y-4">
            <!-- API Configuration -->
            <div>
                <h3 class="text-sm font-semibold text-gray-900 mb-2">API Configuration</h3>
                <div class="space-y-3">
                    <div>
                        <label class="block text-xs font-medium text-gray-700 mb-1">Google Safe Browsing API Key</label>
                        <div class="flex space-x-2">
                            <input id="safeBrowsingKey" type="password" class="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Enter your API key">
                            <button id="testApiKeyBtn" class="px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 text-xs rounded-md transition-colors">
                                Test
                            </button>
                        </div>
                        <div id="apiKeyStatus" class="mt-1 text-xs hidden"></div>
                        <div class="mt-2 p-2 bg-blue-50 rounded-md">
                            <p class="text-xs text-blue-700 mb-1">
                                <strong>Get your free API key:</strong>
                            </p>
                            <ol class="text-xs text-blue-600 space-y-1 ml-3">
                                <li>1. Visit <a href="#" id="googleCloudLink" class="underline hover:text-blue-800">Google Cloud Console</a></li>
                                <li>2. Create/select a project</li>
                                <li>3. Enable "Safe Browsing API"</li>
                                <li>4. Create credentials (API key)</li>
                                <li>5. Copy and paste the key above</li>
                            </ol>
                            <p class="text-xs text-blue-600 mt-2">
                                <strong>Free tier:</strong> 10,000 requests/day
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Protection Settings -->
            <div>
                <h3 class="text-sm font-semibold text-gray-900 mb-2">Protection Settings</h3>
                <div class="space-y-3">
                    <label class="flex items-center">
                        <input id="enableRealTime" type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        <span class="ml-2 text-sm text-gray-700">Real-time scanning</span>
                    </label>
                    <label class="flex items-center">
                        <input id="enableNotifications" type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        <span class="ml-2 text-sm text-gray-700">Show notifications</span>
                    </label>
                    <label class="flex items-center">
                        <input id="enableSSLCheck" type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        <span class="ml-2 text-sm text-gray-700">SSL certificate checking</span>
                    </label>
                </div>
            </div>

            <!-- Data Management -->
            <div>
                <h3 class="text-sm font-semibold text-gray-900 mb-2">Data Management</h3>
                <div class="space-y-2">
                    <button id="exportHistoryBtn" class="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors text-sm">
                        Export Scan History
                    </button>
                    <button id="viewStatsBtn" class="w-full bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-lg transition-colors text-sm">
                        View Statistics
                    </button>
                    <button id="clearHistoryBtn" class="w-full bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors text-sm">
                        Clear History
                    </button>
                </div>
            </div>

            <!-- Save Button -->
            <button id="saveSettingsBtn" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors text-sm">
                Save Settings
            </button>
        </div>
    </div>

    <script src="popup.js"></script>
</body>
</html>
