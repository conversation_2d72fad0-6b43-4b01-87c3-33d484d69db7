/**
 * PhishGuard Popup Script
 * Handles popup UI interactions and displays threat analysis results
 */

// DOM elements
let elements = {};

// Current analysis data
let currentAnalysis = null;
let currentTab = null;

/**
 * Initialize popup when DOM is loaded
 */
document.addEventListener('DOMContentLoaded', async () => {
  try {
    initializeElements();
    setupEventListeners();
    await loadCurrentTab();
    await loadAnalysisData();
    await loadSettings();
    await loadProtectionState();
    await loadRecentActivity();
  } catch (error) {
    console.error('Failed to initialize popup:', error);
    // Show a basic error message if possible
    const errorDiv = document.createElement('div');
    errorDiv.className = 'p-4 text-red-600 text-sm';
    errorDiv.textContent = 'Failed to initialize PhishGuard popup. Please try reloading the extension.';
    document.body.appendChild(errorDiv);
  }
});

/**
 * Initialize DOM element references
 */
function initializeElements() {
  elements = {
    // Main UI elements
    loadingState: document.getElementById('loadingState'),
    mainContent: document.getElementById('mainContent'),
    settingsPanel: document.getElementById('settingsPanel'),

    // Protection toggle elements
    protectionToggle: document.getElementById('protectionToggle'),
    protectionStatusDot: document.getElementById('protectionStatusDot'),
    protectionStatusText: document.getElementById('protectionStatusText'),
    toggleLabel: document.getElementById('toggleLabel'),
    toggleSlider: document.getElementById('toggleSlider'),
    protectionTooltip: document.getElementById('protectionTooltip'),
    tooltipText: document.getElementById('tooltipText'),
    
    // Threat indicator
    threatIndicator: document.getElementById('threatIndicator'),
    threatIcon: document.getElementById('threatIcon'),
    threatTitle: document.getElementById('threatTitle'),
    threatDescription: document.getElementById('threatDescription'),
    
    // Website info
    currentUrl: document.getElementById('currentUrl'),
    domainInfo: document.getElementById('domainInfo'),
    sslIndicator: document.getElementById('sslIndicator'),
    
    // Threat details
    threatsSection: document.getElementById('threatsSection'),
    threatsList: document.getElementById('threatsList'),
    keywordCount: document.getElementById('keywordCount'),
    formCount: document.getElementById('formCount'),
    
    // Buttons
    settingsBtn: document.getElementById('settingsBtn'),
    refreshBtn: document.getElementById('refreshBtn'),
    whitelistBtn: document.getElementById('whitelistBtn'),
    reportBtn: document.getElementById('reportBtn'),
    reportThreatBtn: document.getElementById('reportThreatBtn'),
    feedbackBtn: document.getElementById('feedbackBtn'),
    backBtn: document.getElementById('backBtn'),
    saveSettingsBtn: document.getElementById('saveSettingsBtn'),
    exportHistoryBtn: document.getElementById('exportHistoryBtn'),
    viewStatsBtn: document.getElementById('viewStatsBtn'),
    clearHistoryBtn: document.getElementById('clearHistoryBtn'),
    
    // Settings inputs
    safeBrowsingKey: document.getElementById('safeBrowsingKey'),
    enableRealTime: document.getElementById('enableRealTime'),
    enableNotifications: document.getElementById('enableNotifications'),
    enableSSLCheck: document.getElementById('enableSSLCheck'),
  googleCloudLink: document.getElementById('googleCloudLink'),
  testApiKeyBtn: document.getElementById('testApiKeyBtn'),
  apiKeyStatus: document.getElementById('apiKeyStatus'),
    
    // Recent activity
    recentActivity: document.getElementById('recentActivity')
  };
}

/**
 * Set up event listeners
 */
function setupEventListeners() {
  elements.settingsBtn.addEventListener('click', showSettings);
  elements.backBtn.addEventListener('click', showMainContent);
  elements.refreshBtn.addEventListener('click', refreshAnalysis);
  elements.whitelistBtn.addEventListener('click', addToWhitelist);
  elements.reportBtn.addEventListener('click', reportFalsePositive);
  elements.reportThreatBtn.addEventListener('click', () => showReportingDialog('new_threat'));
  elements.feedbackBtn.addEventListener('click', () => showReportingDialog('feedback'));
  elements.saveSettingsBtn.addEventListener('click', saveSettings);
  elements.exportHistoryBtn.addEventListener('click', exportScanHistory);
  elements.viewStatsBtn.addEventListener('click', showStatistics);
  elements.clearHistoryBtn.addEventListener('click', clearScanHistory);
  elements.googleCloudLink.addEventListener('click', openGoogleCloudConsole);
  elements.testApiKeyBtn.addEventListener('click', testApiKey);
  elements.protectionToggle.addEventListener('click', toggleProtection);

  // Add history view button listener
  const viewHistoryBtn = document.getElementById('viewHistoryBtn');
  if (viewHistoryBtn) {
    viewHistoryBtn.addEventListener('click', showHistoryView);
  }
}

/**
 * Load current active tab
 */
async function loadCurrentTab() {
  try {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    currentTab = tab;
    
    if (tab && tab.url) {
      elements.currentUrl.textContent = tab.url;
      elements.domainInfo.textContent = extractDomain(tab.url);
    }
  } catch (error) {
    console.error('Failed to load current tab:', error);
    showError('Failed to load tab information');
  }
}

/**
 * Load analysis data from background script
 */
async function loadAnalysisData() {
  try {
    // Get analysis from background script
    const response = await chrome.runtime.sendMessage({ type: 'GET_CURRENT_ANALYSIS' });
    
    if (response) {
      currentAnalysis = response;
      updateUI(response);
    } else {
      // No analysis available, trigger new analysis
      await refreshAnalysis();
    }
    
    // Also get page analysis from content script
    if (currentTab) {
      try {
        const pageAnalysis = await chrome.tabs.sendMessage(currentTab.id, { type: 'GET_PAGE_ANALYSIS' });
        if (pageAnalysis) {
          updatePageAnalysisUI(pageAnalysis);
        }
      } catch (error) {
        console.log('Could not get page analysis:', error.message);
      }
    }
    
  } catch (error) {
    console.error('Failed to load analysis data:', error);
    showError('Failed to load threat analysis');
  } finally {
    hideLoading();
  }
}

/**
 * Update UI with analysis results
 * @param {object} analysis - Analysis data
 */
function updateUI(analysis) {
  if (!analysis) return;
  
  // Update threat indicator
  updateThreatIndicator(analysis.threatLevel, analysis.threats);
  
  // Update website info
  if (analysis.url) {
    elements.currentUrl.textContent = analysis.url;
    elements.domainInfo.textContent = analysis.domain || extractDomain(analysis.url);
  }
  
  // Update SSL indicator with detailed information
  updateSSLIndicator(analysis);
  
  // Update threats list
  updateThreatsList(analysis.threats);
  
  // Update whitelist button
  updateWhitelistButton(analysis.domain);
}

/**
 * Update threat indicator display
 * @param {string} threatLevel - Threat level
 * @param {Array} threats - Array of threats
 */
function updateThreatIndicator(threatLevel, threats = []) {
  // Remove existing threat classes
  elements.threatIndicator.classList.remove('threat-safe', 'threat-suspicious', 'threat-dangerous');
  
  // Add appropriate class and update content
  switch (threatLevel) {
    case 'dangerous':
      elements.threatIndicator.classList.add('threat-dangerous');
      elements.threatIcon.textContent = '🚨';
      elements.threatTitle.textContent = 'Dangerous Website';
      elements.threatDescription.textContent = `${threats.length} critical threat${threats.length !== 1 ? 's' : ''} detected`;
      break;
      
    case 'suspicious':
      elements.threatIndicator.classList.add('threat-suspicious');
      elements.threatIcon.textContent = '⚠️';
      elements.threatTitle.textContent = 'Suspicious Website';
      elements.threatDescription.textContent = `${threats.length} potential threat${threats.length !== 1 ? 's' : ''} detected`;
      break;
      
    default:
      elements.threatIndicator.classList.add('threat-safe');
      elements.threatIcon.textContent = '🛡️';
      elements.threatTitle.textContent = 'Safe Website';
      elements.threatDescription.textContent = 'No threats detected';
      break;
  }
}

/**
 * Update threats list display
 * @param {Array} threats - Array of threat descriptions
 */
function updateThreatsList(threats = []) {
  if (threats.length === 0) {
    elements.threatsSection.classList.add('hidden');
    return;
  }
  
  elements.threatsSection.classList.remove('hidden');
  elements.threatsList.innerHTML = '';
  
  threats.forEach(threat => {
    const li = document.createElement('li');
    li.className = 'flex items-start space-x-2 text-sm';
    li.innerHTML = `
      <span class="text-red-500 mt-0.5">•</span>
      <span class="text-gray-700">${escapeHtml(threat)}</span>
    `;
    elements.threatsList.appendChild(li);
  });
}

/**
 * Update page analysis UI
 * @param {object} pageAnalysis - Page analysis data
 */
function updatePageAnalysisUI(pageAnalysis) {
  if (!pageAnalysis) return;
  
  elements.keywordCount.textContent = pageAnalysis.phishingKeywords?.length || 0;
  elements.formCount.textContent = pageAnalysis.insecureForms?.length || 0;
  
  // Add form count styling based on insecure forms
  if (pageAnalysis.insecureForms?.length > 0) {
    elements.formCount.className = 'text-sm font-semibold text-red-600';
  } else {
    elements.formCount.className = 'text-sm font-semibold text-gray-900';
  }
}

/**
 * Update SSL indicator based on analysis
 * @param {object} analysis - Analysis data
 */
function updateSSLIndicator(analysis) {
  let sslClass = 'bg-gray-400'; // Default unknown state
  let title = 'SSL status unknown';

  if (analysis.sslInfo) {
    if (analysis.sslInfo.hasSSL) {
      // Check for SSL-related threats
      const sslThreats = analysis.threats.filter(threat =>
        threat.toLowerCase().includes('ssl') ||
        threat.toLowerCase().includes('security header') ||
        threat.toLowerCase().includes('hsts')
      );

      if (sslThreats.length > 0) {
        sslClass = 'bg-yellow-500';
        title = `HTTPS with issues: ${sslThreats.length} security concern${sslThreats.length !== 1 ? 's' : ''}`;
      } else {
        sslClass = 'bg-green-500';
        title = 'Secure HTTPS connection';
      }
    } else {
      sslClass = 'bg-red-500';
      title = 'Insecure HTTP connection';
    }
  } else if (analysis.url) {
    // Fallback to URL-based detection
    if (analysis.url.startsWith('https://')) {
      sslClass = 'bg-green-500';
      title = 'HTTPS connection';
    } else if (analysis.url.startsWith('http://')) {
      sslClass = 'bg-red-500';
      title = 'Insecure HTTP connection';
    }
  }

  elements.sslIndicator.className = `w-3 h-3 rounded-full ${sslClass}`;
  elements.sslIndicator.title = title;
}

/**
 * Update whitelist button based on domain status
 * @param {string} domain - Current domain
 */
async function updateWhitelistButton(domain) {
  if (!domain) return;

  try {
    // Check if domain is already whitelisted
    const whitelist = await getWhitelist();
    const isWhitelisted = whitelist.includes(domain);

    if (isWhitelisted) {
      elements.whitelistBtn.textContent = 'Remove from Whitelist';
      elements.whitelistBtn.className = 'w-full bg-red-100 hover:bg-red-200 text-red-700 font-medium py-2 px-4 rounded-lg transition-colors text-sm';
    } else {
      elements.whitelistBtn.textContent = 'Add to Whitelist';
      elements.whitelistBtn.className = 'w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors text-sm';
    }
  } catch (error) {
    console.error('Failed to check whitelist status:', error);
  }
}

/**
 * Show settings panel
 */
function showSettings() {
  elements.mainContent.classList.add('hidden');
  elements.settingsPanel.classList.remove('hidden');
}

/**
 * Show main content panel
 */
function showMainContent() {
  elements.settingsPanel.classList.add('hidden');
  elements.mainContent.classList.remove('hidden');
}

/**
 * Refresh analysis for current tab
 */
async function refreshAnalysis() {
  if (!currentTab) return;

  showLoading();

  try {
    // Send message to background script to reanalyze the current tab
    const response = await chrome.runtime.sendMessage({
      type: 'REANALYZE_CURRENT_TAB',
      tabId: currentTab.id,
      url: currentTab.url
    });

    if (response && !response.error) {
      currentAnalysis = response;
      updateUI(response);
    } else {
      showError(response?.error || 'Failed to refresh analysis');
    }
  } catch (error) {
    console.error('Failed to refresh analysis:', error);
    showError('Failed to refresh analysis');
  } finally {
    hideLoading();
  }
}

/**
 * Add or remove domain from whitelist
 */
async function addToWhitelist() {
  if (!currentAnalysis?.domain) return;

  const domain = currentAnalysis.domain;

  try {
    const whitelist = await getWhitelist();
    const isWhitelisted = whitelist.includes(domain);

    if (isWhitelisted) {
      await chrome.runtime.sendMessage({
        type: 'REMOVE_FROM_WHITELIST',
        domain
      });
    } else {
      await chrome.runtime.sendMessage({
        type: 'ADD_TO_WHITELIST',
        domain
      });
    }

    // Update button appearance
    await updateWhitelistButton(domain);

    // Refresh analysis
    await refreshAnalysis();

  } catch (error) {
    console.error('Failed to update whitelist:', error);
    showError('Failed to update whitelist');
  }
}

/**
 * Report false positive
 */
async function reportFalsePositive() {
  if (!currentAnalysis) return;

  showReportingDialog('false_positive');
}

/**
 * Show reporting dialog
 * @param {string} reportType - Type of report ('false_positive', 'new_threat', 'feedback')
 */
function showReportingDialog(reportType) {
  // Create reporting modal
  const modal = document.createElement('div');
  modal.className = 'modal-overlay';

  const dialogContent = createReportingDialogContent(reportType);
  modal.appendChild(dialogContent);

  document.body.appendChild(modal);

  // Add event listeners for buttons
  addPopupEventListeners(modal);

  // Close modal when clicking outside
  modal.addEventListener('click', (e) => {
    if (e.target === modal) {
      modal.remove();
    }
  });
}

/**
 * Create reporting dialog content
 * @param {string} reportType - Type of report
 * @returns {HTMLElement} - Dialog content element
 */
function createReportingDialogContent(reportType) {
  const dialog = document.createElement('div');
  dialog.className = 'modal-dialog';

  const titles = {
    false_positive: 'Report False Positive',
    new_threat: 'Report New Threat',
    feedback: 'Send Feedback'
  };

  const descriptions = {
    false_positive: 'Help us improve by reporting websites incorrectly flagged as threats.',
    new_threat: 'Report a suspicious website that PhishGuard missed.',
    feedback: 'Share your thoughts about PhishGuard.'
  };

  dialog.innerHTML = `
    <div class="mb-4">
      <h3 class="text-lg font-semibold text-gray-900 mb-2">${titles[reportType]}</h3>
      <p class="text-sm text-gray-600">${descriptions[reportType]}</p>
    </div>

    <form id="reportingForm" class="space-y-4">
      ${reportType === 'new_threat' ? `
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Website URL</label>
          <input type="url" id="reportUrl" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="https://example.com" required>
        </div>
      ` : ''}

      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">
          ${reportType === 'false_positive' ? 'Why is this a false positive?' :
            reportType === 'new_threat' ? 'What makes this website suspicious?' :
            'Your feedback'}
        </label>
        <textarea id="reportDescription" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Please provide details..." required></textarea>
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">Email (optional)</label>
        <input type="email" id="reportEmail" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="<EMAIL>">
        <p class="text-xs text-gray-500 mt-1">We may contact you for follow-up questions.</p>
      </div>

      <div class="flex space-x-3 pt-4">
        <button type="submit" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors text-sm">
          Submit Report
        </button>
        <button type="button" data-action="close-modal" class="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-md transition-colors text-sm">
          Cancel
        </button>
      </div>
    </form>
  `;

  // Handle form submission
  const form = dialog.querySelector('#reportingForm');
  form.addEventListener('submit', (e) => {
    e.preventDefault();
    submitReport(reportType, dialog);
  });

  return dialog;
}

/**
 * Submit report to backend
 * @param {string} reportType - Type of report
 * @param {HTMLElement} dialog - Dialog element
 */
async function submitReport(reportType, dialog) {
  const form = dialog.querySelector('#reportingForm');

  const reportData = {
    type: reportType,
    timestamp: Date.now(),
    userAgent: navigator.userAgent,
    extensionVersion: '1.0.0'
  };

  // Collect form data
  if (reportType === 'new_threat') {
    reportData.url = dialog.querySelector('#reportUrl').value;
  } else if (currentAnalysis) {
    reportData.url = currentAnalysis.url;
    reportData.domain = currentAnalysis.domain;
    reportData.threatLevel = currentAnalysis.threatLevel;
    reportData.threats = currentAnalysis.threats;
  }

  reportData.description = dialog.querySelector('#reportDescription').value;
  reportData.email = dialog.querySelector('#reportEmail').value;

  try {
    // Show loading state
    const submitBtn = form.querySelector('button[type="submit"]');
    submitBtn.textContent = 'Submitting...';
    submitBtn.disabled = true;

    // In a real implementation, this would send to a backend service
    // For now, we'll store it locally and log it
    await storeUserReport(reportData);

    // Show success message
    dialog.innerHTML = `
      <div class="text-center py-8">
        <div class="text-green-600 text-4xl mb-4">✓</div>
        <h3 class="text-lg font-semibold text-gray-900 mb-2">Report Submitted</h3>
        <p class="text-sm text-gray-600 mb-4">Thank you for helping improve PhishGuard!</p>
        <button data-action="close-modal" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors text-sm">
          Close
        </button>
      </div>
    `;

  } catch (error) {
    console.error('Failed to submit report:', error);

    // Show error message
    const submitBtn = form.querySelector('button[type="submit"]');
    submitBtn.textContent = 'Failed to Submit';
    submitBtn.className = 'flex-1 bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md transition-colors text-sm';

    setTimeout(() => {
      submitBtn.textContent = 'Submit Report';
      submitBtn.className = 'flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors text-sm';
      submitBtn.disabled = false;
    }, 3000);
  }
}

/**
 * Store user report locally
 * @param {object} reportData - Report data
 */
async function storeUserReport(reportData) {
  try {
    const result = await chrome.storage.local.get('phishguard_user_reports');
    const reports = result.phishguard_user_reports || [];

    reports.unshift(reportData);

    // Keep only last 100 reports
    if (reports.length > 100) {
      reports.splice(100);
    }

    await chrome.storage.local.set({
      phishguard_user_reports: reports
    });

    console.log('User report stored:', reportData);
  } catch (error) {
    console.error('Failed to store user report:', error);
    throw error;
  }
}

/**
 * Export scan history as JSON file
 */
async function exportScanHistory() {
  try {
    const history = await getFilteredScanHistory();
    const stats = await getScanStatistics();

    const exportData = {
      exportDate: new Date().toISOString(),
      extensionVersion: '1.0.0',
      totalRecords: history.length,
      statistics: stats,
      scanHistory: history
    };

    const jsonString = JSON.stringify(exportData, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    // Create download link
    const a = document.createElement('a');
    a.href = url;
    a.download = `phishguard-history-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    // Show success message
    const originalText = elements.exportHistoryBtn.textContent;
    elements.exportHistoryBtn.textContent = 'Exported!';
    elements.exportHistoryBtn.className = 'w-full bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors text-sm';

    setTimeout(() => {
      elements.exportHistoryBtn.textContent = originalText;
      elements.exportHistoryBtn.className = 'w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors text-sm';
    }, 2000);

  } catch (error) {
    console.error('Failed to export history:', error);
    showError('Failed to export scan history');
  }
}

/**
 * Show detailed statistics modal
 */
async function showStatistics() {
  try {
    const stats = await getScanStatistics();
    const history = await getFilteredScanHistory();

    // Calculate additional statistics
    const last7Days = history.filter(scan =>
      scan.timestamp >= Date.now() - (7 * 24 * 60 * 60 * 1000)
    );

    const last30Days = history.filter(scan =>
      scan.timestamp >= Date.now() - (30 * 24 * 60 * 60 * 1000)
    );

    const topDomains = {};
    history.forEach(scan => {
      if (scan.domain) {
        topDomains[scan.domain] = (topDomains[scan.domain] || 0) + 1;
      }
    });

    const sortedDomains = Object.entries(topDomains)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5);

    showStatisticsModal(stats, {
      last7Days: last7Days.length,
      last30Days: last30Days.length,
      topDomains: sortedDomains
    });

  } catch (error) {
    console.error('Failed to load statistics:', error);
    alert('Failed to load statistics');
  }
}

/**
 * Show statistics modal
 * @param {object} stats - Basic statistics
 * @param {object} additionalStats - Additional calculated statistics
 */
function showStatisticsModal(stats, additionalStats) {
  const modal = document.createElement('div');
  modal.className = 'modal-overlay';

  const dialog = document.createElement('div');
  dialog.className = 'modal-dialog-large';

  const safePercentage = stats.totalScans > 0 ? Math.round((stats.safeCount / stats.totalScans) * 100) : 0;
  const suspiciousPercentage = stats.totalScans > 0 ? Math.round((stats.suspiciousCount / stats.totalScans) * 100) : 0;
  const dangerousPercentage = stats.totalScans > 0 ? Math.round((stats.dangerousCount / stats.totalScans) * 100) : 0;

  dialog.innerHTML = `
    <div class="mb-4">
      <h3 class="text-lg font-semibold text-gray-900 mb-2">PhishGuard Statistics</h3>
    </div>

    <div class="space-y-4">
      <!-- Overall Stats -->
      <div class="grid grid-cols-2 gap-4">
        <div class="bg-gray-50 rounded-lg p-3">
          <div class="text-2xl font-bold text-gray-900">${stats.totalScans}</div>
          <div class="text-sm text-gray-600">Total Scans</div>
        </div>
        <div class="bg-gray-50 rounded-lg p-3">
          <div class="text-2xl font-bold text-gray-900">${stats.recentScans.length}</div>
          <div class="text-sm text-gray-600">Last 24 Hours</div>
        </div>
      </div>

      <!-- Threat Level Distribution -->
      <div>
        <h4 class="font-semibold text-gray-900 mb-2">Threat Level Distribution</h4>
        <div class="space-y-2">
          <div class="flex items-center justify-between">
            <span class="text-sm text-green-600">Safe</span>
            <span class="text-sm font-medium">${stats.safeCount} (${safePercentage}%)</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div class="progress-bar progress-bar-safe" data-width="${safePercentage}"></div>
          </div>

          <div class="flex items-center justify-between">
            <span class="text-sm text-yellow-600">Suspicious</span>
            <span class="text-sm font-medium">${stats.suspiciousCount} (${suspiciousPercentage}%)</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div class="progress-bar progress-bar-suspicious" data-width="${suspiciousPercentage}"></div>
          </div>

          <div class="flex items-center justify-between">
            <span class="text-sm text-red-600">Dangerous</span>
            <span class="text-sm font-medium">${stats.dangerousCount} (${dangerousPercentage}%)</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div class="progress-bar progress-bar-dangerous" data-width="${dangerousPercentage}"></div>
          </div>
        </div>
      </div>

      <!-- Activity Timeline -->
      <div>
        <h4 class="font-semibold text-gray-900 mb-2">Recent Activity</h4>
        <div class="grid grid-cols-3 gap-2 text-center">
          <div class="bg-blue-50 rounded p-2">
            <div class="text-lg font-semibold text-blue-600">${additionalStats.last7Days}</div>
            <div class="text-xs text-blue-600">Last 7 Days</div>
          </div>
          <div class="bg-blue-50 rounded p-2">
            <div class="text-lg font-semibold text-blue-600">${additionalStats.last30Days}</div>
            <div class="text-xs text-blue-600">Last 30 Days</div>
          </div>
          <div class="bg-blue-50 rounded p-2">
            <div class="text-lg font-semibold text-blue-600">${stats.totalScans}</div>
            <div class="text-xs text-blue-600">All Time</div>
          </div>
        </div>
      </div>

      <!-- Top Domains -->
      ${additionalStats.topDomains.length > 0 ? `
        <div>
          <h4 class="font-semibold text-gray-900 mb-2">Most Visited Domains</h4>
          <div class="space-y-1">
            ${additionalStats.topDomains.map(([domain, count]) => `
              <div class="flex items-center justify-between text-sm">
                <span class="text-gray-700 truncate">${escapeHtml(domain)}</span>
                <span class="text-gray-500">${count}</span>
              </div>
            `).join('')}
          </div>
        </div>
      ` : ''}
    </div>

    <div class="mt-6 flex space-x-3">
      <button data-action="close-modal" class="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-md transition-colors text-sm">
        Close
      </button>
      <button data-action="export-data" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors text-sm">
        Export Data
      </button>
    </div>
  `;

  modal.appendChild(dialog);
  document.body.appendChild(modal);

  // Set progress bar widths using CSS custom properties
  const progressBars = modal.querySelectorAll('.progress-bar');
  progressBars.forEach(bar => {
    const width = bar.getAttribute('data-width');
    bar.style.setProperty('--progress-width', width + '%');
  });

  // Add event listeners for buttons
  addPopupEventListeners(modal);

  // Close modal when clicking outside
  modal.addEventListener('click', (e) => {
    if (e.target === modal) {
      modal.remove();
    }
  });
}

/**
 * Add event listeners to popup elements
 * @param {HTMLElement} container - Container element to search for buttons
 */
function addPopupEventListeners(container) {
  const buttons = container.querySelectorAll('[data-action]');
  buttons.forEach(button => {
    const action = button.getAttribute('data-action');
    button.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();

      switch (action) {
        case 'close-modal':
          // Find the closest modal and remove it
          const modal = button.closest('.modal-overlay');
          if (modal) {
            modal.remove();
          }
          break;
        case 'export-data':
          exportScanHistory();
          break;
      }
    });
  });
}

/**
 * Clear scan history with confirmation
 */
async function clearScanHistory() {
  const confirmed = confirm(
    'Are you sure you want to clear all scan history?\n\n' +
    'This action cannot be undone. All scan records and statistics will be permanently deleted.'
  );

  if (!confirmed) return;

  try {
    // Clear scan history
    await chrome.storage.local.set({
      phishguard_scan_history: []
    });

    // Also clear user reports
    await chrome.storage.local.set({
      phishguard_user_reports: []
    });

    // Show success message
    const originalText = elements.clearHistoryBtn.textContent;
    elements.clearHistoryBtn.textContent = 'Cleared!';
    elements.clearHistoryBtn.className = 'w-full bg-green-600 text-white font-medium py-2 px-4 rounded-lg transition-colors text-sm';

    setTimeout(() => {
      elements.clearHistoryBtn.textContent = originalText;
      elements.clearHistoryBtn.className = 'w-full bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors text-sm';
    }, 2000);

    // Refresh recent activity display
    await loadRecentActivity();

  } catch (error) {
    console.error('Failed to clear history:', error);
    showError('Failed to clear scan history');
  }
}

/**
 * Load protection state from storage and update toggle UI
 */
async function loadProtectionState() {
  try {
    const result = await chrome.storage.local.get('phishguard_protection_enabled');
    const enabled = result.phishguard_protection_enabled !== false; // Default to true
    updateProtectionToggleUI(enabled);
  } catch (error) {
    console.error('Failed to load protection state:', error);
    // Default to enabled if we can't load the state
    updateProtectionToggleUI(true);
  }
}

/**
 * Load settings from storage
 */
async function loadSettings() {
  try {
    const result = await chrome.storage.local.get('phishguard_settings');
    const settings = result.phishguard_settings || {};

    // Update settings UI
    elements.safeBrowsingKey.value = settings.safeBrowsingApiKey || '';
    elements.enableRealTime.checked = settings.enableRealTimeScanning !== false;
    elements.enableNotifications.checked = settings.showNotifications !== false;
    elements.enableSSLCheck.checked = settings.enableSSLChecking !== false;

  } catch (error) {
    console.error('Failed to load settings:', error);
  }
}

/**
 * Toggle PhishGuard protection on/off
 */
async function toggleProtection() {
  try {
    // Get current protection state
    const result = await chrome.storage.local.get('phishguard_protection_enabled');
    const currentState = result.phishguard_protection_enabled !== false; // Default to true
    const newState = !currentState;

    // Save new state
    await chrome.storage.local.set({ phishguard_protection_enabled: newState });

    // Update UI immediately
    updateProtectionToggleUI(newState);

    // Send message to background script to update protection state
    await chrome.runtime.sendMessage({
      type: 'UPDATE_PROTECTION_STATE',
      enabled: newState
    });

    // Show feedback
    showToggleFeedback(newState);

    // Refresh the current analysis if protection was re-enabled
    if (newState && currentTab) {
      loadCurrentTab();
    }

  } catch (error) {
    console.error('Failed to toggle protection:', error);
    showError('Failed to update protection settings');
  }
}

/**
 * Update the protection toggle UI
 * @param {boolean} enabled - Whether protection is enabled
 */
function updateProtectionToggleUI(enabled) {
  const toggle = elements.protectionToggle;
  const slider = elements.toggleSlider;
  const statusDot = elements.protectionStatusDot;
  const statusText = elements.protectionStatusText;
  const label = elements.toggleLabel;
  const tooltip = elements.protectionTooltip;
  const tooltipText = elements.tooltipText;

  if (enabled) {
    // Enabled state
    toggle.classList.remove('bg-gray-300');
    toggle.classList.add('bg-green-600');
    toggle.setAttribute('aria-checked', 'true');

    slider.classList.remove('translate-x-1');
    slider.classList.add('translate-x-6');

    statusDot.classList.remove('bg-gray-400');
    statusDot.classList.add('bg-green-500');

    statusText.textContent = 'Protection Enabled';
    statusText.classList.remove('text-gray-500');
    statusText.classList.add('text-gray-900');

    label.textContent = 'ON';
    label.classList.remove('text-gray-400');
    label.classList.add('text-gray-500');

    tooltipText.textContent = 'PhishGuard is actively protecting you from malicious websites, phishing attempts, and unsafe forms.';

  } else {
    // Disabled state
    toggle.classList.remove('bg-green-600');
    toggle.classList.add('bg-gray-300');
    toggle.setAttribute('aria-checked', 'false');

    slider.classList.remove('translate-x-6');
    slider.classList.add('translate-x-1');

    statusDot.classList.remove('bg-green-500');
    statusDot.classList.add('bg-gray-400');

    statusText.textContent = 'Protection Disabled';
    statusText.classList.remove('text-gray-900');
    statusText.classList.add('text-gray-500');

    label.textContent = 'OFF';
    label.classList.remove('text-gray-500');
    label.classList.add('text-gray-400');

    tooltipText.textContent = 'PhishGuard protection is disabled. You are not protected from malicious websites.';
  }

  // Show tooltip briefly when toggled
  tooltip.classList.remove('hidden');
  setTimeout(() => {
    tooltip.classList.add('hidden');
  }, 3000);
}

/**
 * Show feedback when protection is toggled
 * @param {boolean} enabled - Whether protection was enabled
 */
function showToggleFeedback(enabled) {
  const message = enabled ? 'Protection Enabled' : 'Protection Disabled';
  const color = enabled ? 'text-green-600' : 'text-orange-600';

  // Create temporary feedback element
  const feedback = document.createElement('div');
  feedback.className = `fixed top-2 right-2 bg-white border border-gray-200 rounded-lg shadow-lg px-3 py-2 z-50 ${color} text-sm font-medium`;
  feedback.textContent = message;

  document.body.appendChild(feedback);

  // Animate in
  feedback.style.opacity = '0';
  feedback.style.transform = 'translateY(-10px)';
  setTimeout(() => {
    feedback.style.transition = 'all 0.3s ease';
    feedback.style.opacity = '1';
    feedback.style.transform = 'translateY(0)';
  }, 10);

  // Remove after 2 seconds
  setTimeout(() => {
    feedback.style.opacity = '0';
    feedback.style.transform = 'translateY(-10px)';
    setTimeout(() => {
      if (feedback.parentNode) {
        feedback.parentNode.removeChild(feedback);
      }
    }, 300);
  }, 2000);
}

/**
 * Open Google Cloud Console for API setup
 */
function openGoogleCloudConsole(event) {
  event.preventDefault();
  const url = 'https://console.cloud.google.com/apis/library/safebrowsing.googleapis.com';
  chrome.tabs.create({ url: url });
}

/**
 * Save settings to storage
 */
async function saveSettings() {
  try {
    const apiKey = elements.safeBrowsingKey.value.trim();

    // Basic API key validation
    if (apiKey && !isValidApiKey(apiKey)) {
      showError('Invalid API key format. Please check your Google Safe Browsing API key.');
      return;
    }

    const settings = {
      safeBrowsingApiKey: apiKey,
      enableRealTimeScanning: elements.enableRealTime.checked,
      showNotifications: elements.enableNotifications.checked,
      enableSSLChecking: elements.enableSSLCheck.checked
    };

    await chrome.runtime.sendMessage({
      type: 'UPDATE_SETTINGS',
      settings
    });

    // Show success message with API key status
    const originalText = elements.saveSettingsBtn.textContent;
    const successMessage = apiKey ? 'Saved! Safe Browsing API enabled' : 'Saved!';
    elements.saveSettingsBtn.textContent = successMessage;
    elements.saveSettingsBtn.className = 'w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors text-sm';

    setTimeout(() => {
      elements.saveSettingsBtn.textContent = originalText;
      elements.saveSettingsBtn.className = 'w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors text-sm';
    }, 3000);

  } catch (error) {
    console.error('Failed to save settings:', error);
    showError('Failed to save settings');
  }
}

/**
 * Test the Safe Browsing API key
 */
async function testApiKey() {
  const apiKey = elements.safeBrowsingKey.value.trim();

  if (!apiKey) {
    showApiKeyStatus('Please enter an API key first', 'error');
    return;
  }

  if (!isValidApiKey(apiKey)) {
    showApiKeyStatus('Invalid API key format', 'error');
    return;
  }

  // Show testing status
  elements.testApiKeyBtn.textContent = 'Testing...';
  elements.testApiKeyBtn.disabled = true;
  showApiKeyStatus('Testing API key...', 'info');

  try {
    // Test with a known safe URL
    const testUrl = 'https://www.google.com';
    const response = await chrome.runtime.sendMessage({
      type: 'TEST_SAFE_BROWSING_API',
      url: testUrl,
      apiKey: apiKey
    });

    if (response.success) {
      showApiKeyStatus('✓ API key is working correctly!', 'success');
    } else {
      showApiKeyStatus(`✗ API test failed: ${response.error}`, 'error');
    }

  } catch (error) {
    console.error('API test failed:', error);
    showApiKeyStatus('✗ API test failed: Network error', 'error');
  } finally {
    elements.testApiKeyBtn.textContent = 'Test';
    elements.testApiKeyBtn.disabled = false;
  }
}

/**
 * Show API key status message
 * @param {string} message - Status message
 * @param {string} type - Message type: 'success', 'error', 'info'
 */
function showApiKeyStatus(message, type) {
  elements.apiKeyStatus.textContent = message;
  elements.apiKeyStatus.className = `mt-1 text-xs ${getStatusColor(type)}`;
  elements.apiKeyStatus.classList.remove('hidden');

  // Auto-hide after 5 seconds for success/info messages
  if (type !== 'error') {
    setTimeout(() => {
      elements.apiKeyStatus.classList.add('hidden');
    }, 5000);
  }
}

/**
 * Get CSS color class for status type
 * @param {string} type - Status type
 * @returns {string} - CSS class
 */
function getStatusColor(type) {
  switch (type) {
    case 'success': return 'text-green-600';
    case 'error': return 'text-red-600';
    case 'info': return 'text-blue-600';
    default: return 'text-gray-600';
  }
}

/**
 * Basic validation for Google API key format
 * @param {string} apiKey - API key to validate
 * @returns {boolean} - True if format looks valid
 */
function isValidApiKey(apiKey) {
  // Google API keys are typically 39 characters long and contain alphanumeric characters and some symbols
  const apiKeyPattern = /^[A-Za-z0-9_-]{35,45}$/;
  return apiKeyPattern.test(apiKey);
}

/**
 * Load recent activity from scan history
 */
async function loadRecentActivity() {
  try {
    // Get recent scans (last 5)
    const recentScans = await getFilteredScanHistory({ limit: 5 });

    if (recentScans.length === 0) {
      elements.recentActivity.innerHTML = '<p class="text-xs text-gray-500">No recent activity</p>';
      return;
    }

    elements.recentActivity.innerHTML = '';

    recentScans.forEach(scan => {
      const activityItem = document.createElement('div');
      activityItem.className = 'flex items-center justify-between py-1';

      const threatColor = {
        safe: 'text-green-600',
        suspicious: 'text-yellow-600',
        dangerous: 'text-red-600'
      }[scan.threatLevel] || 'text-gray-600';

      const timeAgo = formatTimeAgo(scan.timestamp);

      activityItem.innerHTML = `
        <div class="flex-1 min-w-0">
          <p class="text-xs font-medium text-gray-900 truncate">${escapeHtml(scan.domain || 'Unknown')}</p>
          <p class="text-xs ${threatColor}">${scan.threatLevel}</p>
        </div>
        <span class="text-xs text-gray-500">${timeAgo}</span>
      `;

      elements.recentActivity.appendChild(activityItem);
    });

  } catch (error) {
    console.error('Failed to load recent activity:', error);
    elements.recentActivity.innerHTML = '<p class="text-xs text-red-500">Failed to load activity</p>';
  }
}

/**
 * Show history view (placeholder for future implementation)
 */
function showHistoryView() {
  // For now, just show an alert with basic stats
  getScanStatistics().then(stats => {
    alert(`Scan History Statistics:

Total Scans: ${stats.totalScans}
Safe: ${stats.safeCount}
Suspicious: ${stats.suspiciousCount}
Dangerous: ${stats.dangerousCount}
Recent (24h): ${stats.recentScans.length}

Click "Export History" in settings to download full history.`);
  }).catch(error => {
    console.error('Failed to get statistics:', error);
    alert('Failed to load scan statistics');
  });
}

/**
 * Format timestamp as time ago
 * @param {number} timestamp - Timestamp in milliseconds
 * @returns {string} - Formatted time ago string
 */
function formatTimeAgo(timestamp) {
  const now = Date.now();
  const diff = now - timestamp;

  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));

  if (minutes < 1) return 'now';
  if (minutes < 60) return `${minutes}m`;
  if (hours < 24) return `${hours}h`;
  return `${days}d`;
}

/**
 * Get whitelist from storage
 * @returns {Promise<Array>} - Array of whitelisted domains
 */
async function getWhitelist() {
  try {
    const result = await chrome.storage.local.get('phishguard_whitelist');
    return result.phishguard_whitelist || [];
  } catch (error) {
    console.error('Failed to get whitelist:', error);
    return [];
  }
}

/**
 * Get filtered scan history
 * @param {object} filters - Filter criteria
 * @returns {Promise<Array>} - Filtered scan results
 */
async function getFilteredScanHistory(filters = {}) {
  try {
    const result = await chrome.storage.local.get('phishguard_scan_history');
    const history = result.phishguard_scan_history || [];
    let filtered = [...history];

    // Filter by threat level
    if (filters.threatLevel) {
      filtered = filtered.filter(item => item.threatLevel === filters.threatLevel);
    }

    // Filter by date range
    if (filters.startDate) {
      const startTime = new Date(filters.startDate).getTime();
      filtered = filtered.filter(item => item.timestamp >= startTime);
    }

    if (filters.endDate) {
      const endTime = new Date(filters.endDate).getTime();
      filtered = filtered.filter(item => item.timestamp <= endTime);
    }

    // Filter by domain
    if (filters.domain) {
      filtered = filtered.filter(item =>
        item.domain && item.domain.toLowerCase().includes(filters.domain.toLowerCase())
      );
    }

    // Sort by timestamp (newest first)
    filtered.sort((a, b) => b.timestamp - a.timestamp);

    // Limit results
    if (filters.limit) {
      filtered = filtered.slice(0, filters.limit);
    }

    return filtered;
  } catch (error) {
    console.error('Failed to get filtered scan history:', error);
    return [];
  }
}

/**
 * Get scan statistics
 * @returns {Promise<object>} - Statistics object
 */
async function getScanStatistics() {
  try {
    const history = await getFilteredScanHistory();

    const stats = {
      totalScans: history.length,
      safeCount: 0,
      suspiciousCount: 0,
      dangerousCount: 0,
      recentScans: []
    };

    // Count threat levels and collect recent scans
    const now = Date.now();
    const oneDayAgo = now - (24 * 60 * 60 * 1000);

    history.forEach(scan => {
      // Count threat levels
      switch (scan.threatLevel) {
        case 'safe':
          stats.safeCount++;
          break;
        case 'suspicious':
          stats.suspiciousCount++;
          break;
        case 'dangerous':
          stats.dangerousCount++;
          break;
      }

      // Collect recent scans (last 24 hours)
      if (scan.timestamp >= oneDayAgo) {
        stats.recentScans.push(scan);
      }
    });

    return stats;
  } catch (error) {
    console.error('Failed to get scan statistics:', error);
    return {
      totalScans: 0,
      safeCount: 0,
      suspiciousCount: 0,
      dangerousCount: 0,
      recentScans: []
    };
  }
}

/**
 * Extract domain from URL
 * @param {string} url - Full URL
 * @returns {string} - Domain name
 */
function extractDomain(url) {
  try {
    return new URL(url).hostname.toLowerCase();
  } catch {
    return '';
  }
}

/**
 * Escape HTML to prevent XSS
 * @param {string} text - Text to escape
 * @returns {string} - Escaped text
 */
function escapeHtml(text) {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

/**
 * Show loading state
 */
function showLoading() {
  elements.loadingState.classList.remove('hidden');
  elements.mainContent.classList.add('hidden');
}

/**
 * Hide loading state
 */
function hideLoading() {
  elements.loadingState.classList.add('hidden');
  elements.mainContent.classList.remove('hidden');
}

/**
 * Show error message
 * @param {string} message - Error message
 */
function showError(message) {
  console.error('Error:', message);

  // Update threat indicator to show error (with safety checks)
  try {
    if (elements.threatIndicator) {
      elements.threatIndicator.classList.remove('threat-safe', 'threat-suspicious', 'threat-dangerous');
      elements.threatIndicator.classList.add('threat-suspicious');
    }
    if (elements.threatIcon) {
      elements.threatIcon.textContent = '❌';
    }
    if (elements.threatTitle) {
      elements.threatTitle.textContent = 'Analysis Error';
    }
    if (elements.threatDescription) {
      elements.threatDescription.textContent = message;
    }
  } catch (error) {
    console.error('Failed to update error UI:', error);
  }

  hideLoading();
}

console.log('PhishGuard popup script loaded');
